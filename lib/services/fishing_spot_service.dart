import 'dart:io';
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:collection/collection.dart';
import '../models/fishing_spot.dart';
import '../config/pocketbase_config.dart';

/// 钓点服务类，用于管理钓点数据
class FishingSpotService {
  // 钓点数据缓存
  List<FishingSpot> _spots = [];

  // 区域缓存，用于存储已加载的区域内的钓点
  final Map<String, List<FishingSpot>> _regionCache = {};

  // 缓存过期时间（毫秒）
  static const int _cacheDuration = 5 * 60 * 1000; // 5分钟

  // 缓存时间戳
  DateTime _lastAllSpotsLoadTime = DateTime.now().subtract(
    const Duration(days: 1),
  );
  final Map<String, DateTime> _regionCacheTime = {};

  // 单例模式
  static final FishingSpotService _instance = FishingSpotService._internal();

  factory FishingSpotService() {
    return _instance;
  }

  FishingSpotService._internal();

  /// 获取所有钓点
  Future<List<FishingSpot>> getAllSpots() async {
    // 检查缓存是否有效
    final now = DateTime.now();
    if (_spots.isNotEmpty &&
        now.difference(_lastAllSpotsLoadTime).inMilliseconds < _cacheDuration) {
      debugPrint('使用缓存的所有钓点数据');
      return _spots;
    }

    try {
      // 从 PocketBase 获取钓点数据
      final records = await pb
          .collection('fishing_spots')
          .getFullList(
            sort: '-created',
            expand: 'user_id,spot_photos(spot_id),comments(spot_id).user_id',
          );

      // 转换为FishingSpot对象并更新缓存
      _spots = _convertRecordsToSpots(records);
      _lastAllSpotsLoadTime = now;

      // 清理区域缓存，因为全局数据已更新
      _regionCache.clear();
      _regionCacheTime.clear();

      return _spots;
    } catch (e) {
      debugPrint('获取钓点失败: $e');

      // 如果API调用失败但有缓存，返回缓存
      if (_spots.isNotEmpty) {
        debugPrint('使用过期的钓点缓存数据');
        return _spots;
      }

      // 如果没有缓存，尝试从本地加载
      await _loadSpots();
      return _spots;
    }
  }

  /// 根据地图范围获取钓点
  Future<List<FishingSpot>> getSpotsInBounds(
    double minLat,
    double minLng,
    double maxLat,
    double maxLng, {
    int limit = 30,
  }) async {
    // 生成区域缓存键
    final regionKey =
        '${minLat.toStringAsFixed(2)},${minLng.toStringAsFixed(2)},${maxLat.toStringAsFixed(2)},${maxLng.toStringAsFixed(2)},$limit';

    // 检查缓存是否有效
    final now = DateTime.now();
    if (_regionCache.containsKey(regionKey) &&
        _regionCacheTime.containsKey(regionKey) &&
        now.difference(_regionCacheTime[regionKey]!).inMilliseconds <
            _cacheDuration) {
      debugPrint('使用区域缓存的钓点数据: $regionKey');
      return _regionCache[regionKey]!;
    }

    try {
      // PocketBase 没有内置地理查询，所以我们获取所有钓点然后在客户端过滤
      // 为了性能考虑，我们可以添加一些基本的过滤条件
      final records = await pb
          .collection('fishing_spots')
          .getList(
            page: 1,
            perPage: limit * 2, // 获取更多数据以便过滤
            sort: '-created',
            expand: 'user_id,spot_photos(spot_id),comments(spot_id).user_id',
            filter:
                'latitude >= $minLat && latitude <= $maxLat && longitude >= $minLng && longitude <= $maxLng',
          );

      // 转换并缓存结果
      final allSpots = _convertRecordsToSpots(records.items);

      // 在客户端进行精确的地理范围过滤
      final spots =
          allSpots
              .where((spot) {
                return spot.location.latitude >= minLat &&
                    spot.location.latitude <= maxLat &&
                    spot.location.longitude >= minLng &&
                    spot.location.longitude <= maxLng;
              })
              .take(limit)
              .toList();

      _regionCache[regionKey] = spots;
      _regionCacheTime[regionKey] = now;

      return spots;
    } catch (e) {
      debugPrint('获取范围内钓点失败: $e');

      // 如果API调用失败但有缓存，返回过期的缓存
      if (_regionCache.containsKey(regionKey)) {
        debugPrint('使用过期的区域缓存数据');
        return _regionCache[regionKey]!;
      }

      // 如果没有缓存，尝试从全局缓存中筛选
      if (_spots.isNotEmpty) {
        debugPrint('从全局缓存筛选区域内的钓点');
        return _spots
            .where((spot) {
              return spot.location.latitude >= minLat &&
                  spot.location.latitude <= maxLat &&
                  spot.location.longitude >= minLng &&
                  spot.location.longitude <= maxLng;
            })
            .take(limit)
            .toList();
      }

      // 如果全局缓存也为空，返回空列表
      return [];
    }
  }

  /// 添加新钓点
  Future<FishingSpot?> addSpot(FishingSpot spot) async {
    try {
      // 检查用户是否已登录
      if (!pb.authStore.isValid) {
        throw Exception('用户未登录');
      }

      final userId = pb.authStore.record?.id;
      if (userId == null) {
        throw Exception('无法获取用户ID');
      }

      // 将钓点数据插入到 PocketBase
      final record = await pb
          .collection('fishing_spots')
          .create(
            body: {
              'user_id': userId,
              'name': spot.name,
              'latitude': spot.location.latitude,
              'longitude': spot.location.longitude,
              'description': spot.description,
              'likes': 0,
              'unlikes': 0,
            },
          );

      // 更新用户的已发布钓点列表
      try {
        final userRecord = await pb.collection('users').getOne(userId);
        final publishedSpots = List<String>.from(
          userRecord.data['published_spots'] ?? [],
        );
        publishedSpots.add(record.id);

        await pb
            .collection('users')
            .update(userId, body: {'published_spots': publishedSpots});
      } catch (e) {
        debugPrint('更新用户已发布钓点列表失败: $e');
      }

      // 如果有照片，上传照片
      if (spot.photoUrls.isNotEmpty) {
        for (final photoPath in spot.photoUrls) {
          await addPhotoToSpot(record.id, photoPath, false);
        }
      }

      // 如果有全景照片，上传全景照片
      if (spot.panoramaPhotoUrl != null) {
        await addPhotoToSpot(record.id, spot.panoramaPhotoUrl!, true);
      }

      // 获取完整的钓点数据
      return await getSpotById(record.id);
    } catch (e) {
      debugPrint('添加钓点失败: $e');
      return null;
    }
  }

  /// 更新钓点
  Future<bool> updateSpot(FishingSpot spot) async {
    try {
      // 检查用户是否已登录
      if (!pb.authStore.isValid) {
        throw Exception('用户未登录');
      }

      final userId = pb.authStore.record?.id;
      if (userId == null) {
        throw Exception('无法获取用户ID');
      }

      // 先检查钓点是否属于当前用户
      final existingRecord = await pb
          .collection('fishing_spots')
          .getOne(spot.id);
      if (existingRecord.data['user_id'] != userId) {
        throw Exception('无权限更新此钓点');
      }

      // 更新钓点数据
      await pb
          .collection('fishing_spots')
          .update(
            spot.id,
            body: {
              'name': spot.name,
              'latitude': spot.location.latitude,
              'longitude': spot.location.longitude,
              'description': spot.description,
            },
          );

      return true;
    } catch (e) {
      debugPrint('更新钓点失败: $e');
      return false;
    }
  }

  /// 删除钓点
  Future<bool> deleteSpot(String id) async {
    try {
      // 检查用户是否已登录
      if (!pb.authStore.isValid) {
        throw Exception('用户未登录');
      }

      final userId = pb.authStore.record?.id;
      if (userId == null) {
        throw Exception('无法获取用户ID');
      }

      // 先检查钓点是否属于当前用户
      final existingRecord = await pb.collection('fishing_spots').getOne(id);
      if (existingRecord.data['user_id'] != userId) {
        throw Exception('无权限删除此钓点');
      }

      // 删除钓点
      await pb.collection('fishing_spots').delete(id);

      // 更新用户的已发布钓点列表
      try {
        final userRecord = await pb.collection('users').getOne(userId);
        final publishedSpots = List<String>.from(
          userRecord.data['published_spots'] ?? [],
        );
        publishedSpots.remove(id);

        await pb
            .collection('users')
            .update(userId, body: {'published_spots': publishedSpots});
      } catch (e) {
        debugPrint('更新用户已发布钓点列表失败: $e');
      }

      return true;
    } catch (e) {
      debugPrint('删除钓点失败: $e');
      return false;
    }
  }

  /// 获取指定ID的钓点
  Future<FishingSpot?> getSpotById(String id) async {
    try {
      final record = await pb
          .collection('fishing_spots')
          .getOne(
            id,
            expand: 'user_id,spot_photos(spot_id),comments(spot_id).user_id',
          );

      final spots = _convertRecordsToSpots([record]);
      return spots.isNotEmpty ? spots.first : null;
    } catch (e) {
      debugPrint('获取钓点详情失败: $e');
      // 尝试从本地缓存获取
      return _spots.firstWhereOrNull((spot) => spot.id == id);
    }
  }

  /// 添加评论
  Future<bool> addComment(String spotId, String content) async {
    try {
      // 检查用户是否已登录
      if (!pb.authStore.isValid) {
        throw Exception('用户未登录');
      }

      final userId = pb.authStore.record?.id;
      if (userId == null) {
        throw Exception('无法获取用户ID');
      }

      // 添加评论
      await pb
          .collection('comments')
          .create(
            body: {'spot_id': spotId, 'user_id': userId, 'content': content},
          );

      return true;
    } catch (e) {
      debugPrint('添加评论失败: $e');
      return false;
    }
  }

  /// 添加点赞
  Future<bool> addLike(String spotId) async {
    try {
      // 获取当前钓点数据
      final record = await pb.collection('fishing_spots').getOne(spotId);
      final currentLikes = record.data['likes'] ?? 0;

      // 更新钓点点赞数
      await pb
          .collection('fishing_spots')
          .update(spotId, body: {'likes': currentLikes + 1});

      return true;
    } catch (e) {
      debugPrint('点赞失败: $e');
      return false;
    }
  }

  /// 添加不喜欢
  Future<bool> addUnlike(String spotId) async {
    try {
      // 获取当前钓点数据
      final record = await pb.collection('fishing_spots').getOne(spotId);
      final currentUnlikes = record.data['unlikes'] ?? 0;

      // 更新钓点不喜欢数
      await pb
          .collection('fishing_spots')
          .update(spotId, body: {'unlikes': currentUnlikes + 1});

      return true;
    } catch (e) {
      debugPrint('不喜欢失败: $e');
      return false;
    }
  }

  /// 上传照片并添加到钓点
  Future<String?> addPhotoToSpot(
    String spotId,
    String photoPath,
    bool isPanorama,
  ) async {
    try {
      // 检查用户是否已登录
      if (!pb.authStore.isValid) {
        throw Exception('用户未登录');
      }

      // 上传文件到 PocketBase
      final file = File(photoPath);
      final formData = <String, dynamic>{
        'spot_id': spotId,
        'is_panorama': isPanorama,
        'photo': file,
      };

      // 创建照片记录并上传文件
      final record = await pb.collection('spot_photos').create(body: formData);

      // 获取文件URL
      final photoUrl = pb.files.getUrl(record, record.data['photo']).toString();

      return photoUrl;
    } catch (e) {
      debugPrint('上传照片失败: $e');
      return null;
    }
  }

  /// 从 PocketBase 记录转换为 FishingSpot 对象列表
  List<FishingSpot> _convertRecordsToSpots(List<dynamic> records) {
    return records.map((record) {
      // 获取用户名
      final userData = record.expand['user_id'];
      final username = userData?.data['username'] ?? 'Unknown';

      // 处理照片
      final photos =
          record.expand['spot_photos(spot_id)'] as List<dynamic>? ?? [];
      final photoUrls = <String>[];
      String? panoramaPhotoUrl;

      for (final photo in photos) {
        if (photo.data['is_panorama'] == true) {
          panoramaPhotoUrl = photo.data['photo_url'];
        } else {
          photoUrls.add(photo.data['photo_url']);
        }
      }

      // 处理评论
      final commentsData =
          record.expand['comments(spot_id)'] as List<dynamic>? ?? [];
      final comments =
          commentsData.map((comment) {
            final commentUserData = comment.expand['user_id'];
            return Comment(
              id: comment.id,
              username: commentUserData?.data['username'] ?? 'Unknown',
              content: comment.data['content'],
              createdAt: DateTime.parse(comment.data['created']),
            );
          }).toList();

      // 创建钓点对象
      return FishingSpot(
        id: record.id,
        location: LatLng(record.data['latitude'], record.data['longitude']),
        name: record.data['name'],
        sharedBy: username,
        likes: record.data['likes'] ?? 0,
        unlikes: record.data['unlikes'] ?? 0,
        comments: comments,
        photoUrls: photoUrls,
        panoramaPhotoUrl: panoramaPhotoUrl,
        createdAt: DateTime.parse(record.data['created']),
        description: record.data['description'] ?? '',
      );
    }).toList();
  }

  /// 从本地存储加载钓点数据（备用方法）
  Future<void> _loadSpots() async {
    // 如果无法从Supabase加载数据，使用示例数据
    await generateSampleData();
  }

  /// 生成示例数据
  Future<void> generateSampleData() async {
    if (_spots.isEmpty) {
      _spots = [
        FishingSpot(
          id: '1',
          location: LatLng(39.9087, 116.3975),
          name: '北京天安门广场',
          sharedBy: '钓鱼达人',
          likes: 15,
          unlikes: 2,
          comments: [
            Comment(
              id: 'c1',
              username: '钓友小王',
              content: '这里环境不错，适合休闲钓鱼',
              createdAt: DateTime.now().subtract(const Duration(days: 5)),
            ),
            Comment(
              id: 'c2',
              username: '垂钓者',
              content: '水质清澈，鱼种丰富，推荐！',
              createdAt: DateTime.now().subtract(const Duration(days: 3)),
            ),
          ],
          photoUrls: ['https://example.com/photo1.jpg'],
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          description: '这是一个示例钓点，环境优美，鱼种丰富。周边设施完善，交通便利，适合全家出行。',
        ),
        FishingSpot(
          id: '2',
          location: LatLng(39.9837, 116.2973),
          name: '颐和园昆明湖',
          sharedBy: '钓友小王',
          likes: 28,
          unlikes: 1,
          comments: [
            Comment(
              id: 'c3',
              username: '钓鱼达人',
              content: '风景优美，适合休闲垂钓',
              createdAt: DateTime.now().subtract(const Duration(days: 10)),
            ),
          ],
          photoUrls: ['https://example.com/photo2.jpg'],
          createdAt: DateTime.now().subtract(const Duration(days: 20)),
          description: '昆明湖水域广阔，环境优美，是休闲垂钓的好去处。湖中鱼种较多，主要有鲤鱼、鲫鱼等。',
        ),
        FishingSpot(
          id: '3',
          location: LatLng(31.2304, 121.4737),
          name: '上海外滩黄浦江',
          sharedBy: '垂钓者',
          likes: 42,
          unlikes: 5,
          comments: [
            Comment(
              id: 'c4',
              username: '钓鱼达人',
              content: '夜景很美，但人比较多',
              createdAt: DateTime.now().subtract(const Duration(days: 7)),
            ),
            Comment(
              id: 'c5',
              username: '钓友小王',
              content: '鱼获不错，主要是鲫鱼和鲢鱼',
              createdAt: DateTime.now().subtract(const Duration(days: 6)),
            ),
          ],
          photoUrls: ['https://example.com/photo3.jpg'],
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
          description: '黄浦江边垂钓，可以欣赏上海美丽的城市夜景。江中鱼种丰富，适合有经验的钓友。',
        ),
        FishingSpot(
          id: '4',
          location: LatLng(22.5431, 114.0579),
          name: '深圳湾公园',
          sharedBy: '钓鱼达人',
          likes: 36,
          unlikes: 3,
          comments: [],
          photoUrls: ['https://example.com/photo4.jpg'],
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          description: '深圳湾公园环境优美，空气清新，是休闲垂钓的好去处。这里靠近海边，可以钓到一些海鱼。',
        ),
        FishingSpot(
          id: '5',
          location: LatLng(30.2743, 120.1551),
          name: '杭州西湖',
          sharedBy: '垂钓者',
          likes: 53,
          unlikes: 2,
          comments: [
            Comment(
              id: 'c6',
              username: '钓友小王',
              content: '风景如画，适合休闲钓鱼',
              createdAt: DateTime.now().subtract(const Duration(days: 4)),
            ),
          ],
          photoUrls: ['https://example.com/photo5.jpg'],
          createdAt: DateTime.now().subtract(const Duration(days: 8)),
          description: '西湖景色优美，环境宜人，是休闲垂钓的绝佳场所。湖中鱼种丰富，主要有鲤鱼、鲫鱼、草鱼等。',
        ),
      ];
      _spots = _spots;
    }
  }
}
