import 'package:flutter/material.dart';
import '../../services/fishing_spot_service.dart';
import '../../services/pocketbase_auth_service.dart';
import '../../models/user.dart' as model_user;
import '../../models/fishing_spot.dart';
import '../../config/pocketbase_config.dart';

/// PocketBase功能测试页面
/// 仅在开发模式下可用，用于测试各种PocketBase后端功能
class PocketBaseTestPage extends StatefulWidget {
  const PocketBaseTestPage({super.key});

  @override
  State<PocketBaseTestPage> createState() => _PocketBaseTestPageState();
}

class _PocketBaseTestPageState extends State<PocketBaseTestPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // 服务实例
  final FishingSpotService _spotService = FishingSpotService();
  final PocketBaseAuthService _authService = PocketBaseAuthService();

  // 测试数据控制器
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _nicknameController = TextEditingController();

  // 状态变量
  bool _isLoading = false;
  String _statusMessage = '';
  List<model_user.User> _users = [];
  List<FishingSpot> _spots = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeTestData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _usernameController.dispose();
    _nicknameController.dispose();
    super.dispose();
  }

  void _initializeTestData() {
    _emailController.text = '<EMAIL>';
    _passwordController.text = 'test123456';
    _usernameController.text = 'testuser';
    _nicknameController.text = '测试用户';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PocketBase 测试'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '认证测试'),
            Tab(text: '用户管理'),
            Tab(text: '钓点管理'),
            Tab(text: '系统信息'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAuthTestTab(),
          _buildUserManagementTab(),
          _buildSpotManagementTab(),
          _buildSystemInfoTab(),
        ],
      ),
    );
  }

  /// 构建认证测试标签页
  Widget _buildAuthTestTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 当前认证状态
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '当前认证状态',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text('用户ID: ${pb.authStore.record?.id ?? '未登录'}'),
                  Text('邮箱: ${pb.authStore.record?.data['email'] ?? '无'}'),
                  Text('登录状态: ${_authService.isLoggedIn ? '已登录' : '未登录'}'),
                  Text('当前用户: ${_authService.currentUser?.username ?? '无'}'),
                  Text('昵称: ${_authService.currentUser?.nickname ?? '无'}'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 登录测试
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('登录测试', style: Theme.of(context).textTheme.titleMedium),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _emailController,
                    decoration: const InputDecoration(
                      labelText: '邮箱',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _passwordController,
                    decoration: const InputDecoration(
                      labelText: '密码',
                      border: OutlineInputBorder(),
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _testLogin,
                          child: const Text('测试登录'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _testLogout,
                          child: const Text('退出登录'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 状态信息
          if (_statusMessage.isNotEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '测试结果',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(_statusMessage),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建用户管理标签页
  Widget _buildUserManagementTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 用户列表
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '用户列表 (${_users.length})',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      ElevatedButton(
                        onPressed: _isLoading ? null : _refreshUsers,
                        child: const Text('刷新'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_users.isEmpty)
                    const Text('暂无用户数据')
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _users.length,
                      itemBuilder: (context, index) {
                        final user = _users[index];
                        return ListTile(
                          title: Text(user.username),
                          subtitle: Text(user.email),
                          trailing: Text(
                            user.nickname.isEmpty ? '无昵称' : user.nickname,
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 创建测试用户
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '创建测试用户',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _usernameController,
                    decoration: const InputDecoration(
                      labelText: '用户名',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _nicknameController,
                    decoration: const InputDecoration(
                      labelText: '昵称',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _createTestUser,
                    child: const Text('创建测试用户'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建钓点管理标签页
  Widget _buildSpotManagementTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 钓点列表
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '钓点列表 (${_spots.length})',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      ElevatedButton(
                        onPressed: _isLoading ? null : _refreshSpots,
                        child: const Text('刷新'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_spots.isEmpty)
                    const Text('暂无钓点数据')
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _spots.length,
                      itemBuilder: (context, index) {
                        final spot = _spots[index];
                        return ListTile(
                          title: Text(spot.name),
                          subtitle: Text(
                            '${spot.location.latitude.toStringAsFixed(4)}, ${spot.location.longitude.toStringAsFixed(4)}',
                          ),
                          trailing: Text(spot.sharedBy),
                        );
                      },
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建系统信息标签页
  Widget _buildSystemInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'PocketBase 连接信息',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  Text('服务器地址: ${PocketBaseConfig.instance.serverUrl}'),
                  Text(
                    '认证状态: ${PocketBaseConfig.instance.isAuthenticated ? '已认证' : '未认证'}',
                  ),
                  Text(
                    '当前用户ID: ${PocketBaseConfig.instance.currentUserId ?? '无'}',
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 测试登录
  Future<void> _testLogin() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在测试登录...';
    });

    try {
      final user = await _authService.login(
        email: _emailController.text,
        password: _passwordController.text,
      );

      if (user != null) {
        _updateStatus('登录成功: ${user.username}');
      } else {
        _updateStatus('登录失败: 用户不存在或密码错误');
      }
    } catch (e) {
      _updateStatus('登录失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试退出登录
  Future<void> _testLogout() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在退出登录...';
    });

    try {
      await _authService.logout();
      _updateStatus('退出登录成功');
    } catch (e) {
      _updateStatus('退出登录失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 刷新用户列表
  Future<void> _refreshUsers() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在加载用户列表...';
    });

    try {
      final records = await pb
          .collection('users')
          .getList(page: 1, perPage: 50, sort: '-created');

      _users =
          records.items.map((record) {
            return model_user.User.fromJson(record.toJson());
          }).toList();

      _updateStatus('用户列表加载成功，共 ${_users.length} 个用户');
    } catch (e) {
      _updateStatus('加载用户列表失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 创建测试用户
  Future<void> _createTestUser() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在创建测试用户...';
    });

    try {
      final user = await _authService.register(
        email: '${_usernameController.text}@test.com',
        password: 'test123456',
        username: _usernameController.text,
        nickname: _nicknameController.text,
      );

      if (user != null) {
        _updateStatus('测试用户创建成功: ${user.username}');
        await _refreshUsers();
      } else {
        _updateStatus('创建测试用户失败');
      }
    } catch (e) {
      _updateStatus('创建测试用户失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 刷新钓点列表
  Future<void> _refreshSpots() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在加载钓点列表...';
    });

    try {
      _spots = await _spotService.getAllSpots();
      _updateStatus('钓点列表加载成功，共 ${_spots.length} 个钓点');
    } catch (e) {
      _updateStatus('加载钓点列表失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 更新状态信息
  void _updateStatus(String message) {
    setState(() {
      _statusMessage = message;
    });

    // 显示snackbar
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    }
  }
}
