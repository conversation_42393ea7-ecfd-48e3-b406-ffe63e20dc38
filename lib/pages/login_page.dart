import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/user_service.dart';
import '../services/enhanced_auth_service.dart';
import '../widgets/snackbar.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  // 服务
  final UserService _userService = UserService();
  final EnhancedAuthService _authService = EnhancedAuthService();

  // 登录相关控制器和状态
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  final FocusNode _confirmPasswordFocusNode = FocusNode();
  final FocusNode _phoneFocusNode = FocusNode();

  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;
  bool _isRegisterMode = false; // 是否为注册模式
  bool _isPhoneLoginMode = false; // 是否为手机号登录模式

  // 错误信息
  String? _emailError;
  String? _passwordError;
  String? _confirmPasswordError;
  String? _phoneError;

  // SharedPreferences 键
  static const String _emailKey = 'saved_email';
  static const String _passwordKey = 'saved_password';

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
    _loadSavedCredentials();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  // 检查登录状态
  Future<void> _checkLoginStatus() async {
    if (_userService.isLoggedIn()) {
      if (mounted) {
        Navigator.pop(context);
      }
    }
  }

  // 加载保存的凭据
  Future<void> _loadSavedCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedEmail = prefs.getString(_emailKey);
      final savedPassword = prefs.getString(_passwordKey);

      if (savedEmail != null) {
        _emailController.text = savedEmail;
      }
      if (savedPassword != null) {
        _passwordController.text = savedPassword;
      }
    } catch (e) {
      debugPrint('加载保存的凭据失败: $e');
    }
  }

  // 保存凭据
  Future<void> _saveCredentials(String email, String password) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_emailKey, email);
      await prefs.setString(_passwordKey, password);
    } catch (e) {
      debugPrint('保存凭据失败: $e');
    }
  }

  // 登录/注册逻辑
  Future<void> _handleLogin() async {
    final email = _emailController.text.trim();
    final password = _passwordController.text.trim();
    final confirmPassword = _confirmPasswordController.text.trim();

    // 清除之前的错误
    setState(() {
      _emailError = null;
      _passwordError = null;
      _confirmPasswordError = null;
    });

    // 验证邮箱
    if (email.isEmpty) {
      setState(() {
        _emailError = '请输入邮箱';
      });
      return;
    }

    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      setState(() {
        _emailError = '请输入有效的邮箱地址';
      });
      return;
    }

    // 验证密码
    if (password.isEmpty) {
      setState(() {
        _passwordError = '请输入密码';
      });
      return;
    }

    // 如果是注册模式，验证确认密码
    if (_isRegisterMode) {
      if (password.length < 6) {
        setState(() {
          _passwordError = '密码长度不能少于6位';
        });
        return;
      }

      if (confirmPassword.isEmpty) {
        setState(() {
          _confirmPasswordError = '请确认密码';
        });
        return;
      }

      if (password != confirmPassword) {
        setState(() {
          _confirmPasswordError = '两次输入的密码不一致';
        });
        return;
      }
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final user =
          _isPhoneLoginMode
              ? await _authService.phoneLogin(
                phoneNumber: _phoneController.text.trim(),
              )
              : await _authService.login(email: email, password: password);

      if (user != null && mounted) {
        // 保存凭据
        if (!_isPhoneLoginMode) {
          await _saveCredentials(email, password);
        }

        if (mounted) {
          CustomSnackBar(
            context,
            Text(
              _isRegisterMode ? '注册成功' : '登录成功',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.green,
          );
          Navigator.pushReplacementNamed(context, '/main');
        }
      } else if (mounted) {
        // 检查是否是因为用户不存在而失败，如果是则切换到注册模式
        if (!_isRegisterMode && !_isPhoneLoginMode) {
          setState(() {
            _isRegisterMode = true;
            _passwordError = '用户不存在，请注册新账户';
          });
        } else {
          setState(() {
            if (_isPhoneLoginMode) {
              _phoneError = '手机号登录失败，请稍后重试';
            } else {
              _passwordError = '注册失败，请稍后重试';
            }
          });
        }
      }
    } catch (e) {
      if (mounted) {
        final errorMessage = e.toString();
        if (errorMessage.contains('Invalid login credentials') ||
            errorMessage.contains('Email not confirmed')) {
          setState(() {
            _passwordError = '密码错误';
          });
        } else if (errorMessage.contains('User already registered')) {
          setState(() {
            _isRegisterMode = false;
            _passwordError = '用户已存在，请直接登录';
          });
        } else {
          setState(() {
            if (_isPhoneLoginMode) {
              _phoneError = '手机号登录失败：${e.toString()}';
            } else {
              _passwordError = '登录失败：${e.toString()}';
            }
          });
        }
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 切换密码可见性
  void _togglePasswordVisibility() {
    setState(() {
      _obscurePassword = !_obscurePassword;
    });
  }

  // 切换确认密码可见性
  void _toggleConfirmPasswordVisibility() {
    setState(() {
      _obscureConfirmPassword = !_obscureConfirmPassword;
    });
  }

  // 构建输入框
  Widget _buildInputField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String hintText,
    required IconData icon,
    String? errorText,
    bool obscureText = false,
    VoidCallback? onToggleVisibility,
    TextInputType keyboardType = TextInputType.text,
    TextInputAction textInputAction = TextInputAction.next,
    VoidCallback? onSubmitted,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: controller,
          focusNode: focusNode,
          obscureText: obscureText,
          keyboardType: keyboardType,
          textInputAction: textInputAction,
          onSubmitted: (_) => onSubmitted?.call(),
          decoration: InputDecoration(
            hintText: hintText,
            prefixIcon: Icon(icon),
            suffixIcon:
                onToggleVisibility != null
                    ? IconButton(
                      icon: Icon(
                        obscureText ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: onToggleVisibility,
                    )
                    : null,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            errorText: null, // 不在这里显示错误，使用下面的红色文本
          ),
        ),
        if (errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 12),
            child: Text(
              errorText,
              style: const TextStyle(color: Colors.red, fontSize: 12),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          _isRegisterMode ? '注册' : '登录',
          style: const TextStyle(color: Colors.black),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 40),

              // App Logo
              Center(
                child: Image.asset(
                  'assets/images/logo.png',
                  height: 120,
                  width: 120,
                  fit: BoxFit.contain,
                ),
              ),

              const SizedBox(height: 24),

              // 标题
              Text(
                '欢迎使用鱼一窝',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 8),

              Text(
                _isRegisterMode ? '创建新账户' : '登录您的账户',
                style: const TextStyle(fontSize: 16, color: Colors.grey),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 40),

              // 邮箱输入框
              _buildInputField(
                controller: _emailController,
                focusNode: _emailFocusNode,
                hintText: _emailError ?? '输入邮箱',
                icon: Icons.email_outlined,
                errorText: _emailError,
                keyboardType: TextInputType.emailAddress,
                onSubmitted: () => _passwordFocusNode.requestFocus(),
              ),

              const SizedBox(height: 16),

              // 密码输入框
              _buildInputField(
                controller: _passwordController,
                focusNode: _passwordFocusNode,
                hintText: _passwordError ?? '输入密码',
                icon: Icons.lock_outline,
                errorText: _passwordError,
                obscureText: _obscurePassword,
                onToggleVisibility: _togglePasswordVisibility,
                onSubmitted:
                    _isRegisterMode
                        ? () => _confirmPasswordFocusNode.requestFocus()
                        : _handleLogin,
                textInputAction:
                    _isRegisterMode
                        ? TextInputAction.next
                        : TextInputAction.done,
              ),

              // 确认密码输入框（仅在注册模式显示）
              if (_isRegisterMode) ...[
                const SizedBox(height: 16),
                _buildInputField(
                  controller: _confirmPasswordController,
                  focusNode: _confirmPasswordFocusNode,
                  hintText: _confirmPasswordError ?? '确认密码',
                  icon: Icons.lock_outline,
                  errorText: _confirmPasswordError,
                  obscureText: _obscureConfirmPassword,
                  onToggleVisibility: _toggleConfirmPasswordVisibility,
                  onSubmitted: _handleLogin,
                  textInputAction: TextInputAction.done,
                ),
              ],

              const SizedBox(height: 24),

              // 登录/注册按钮
              ElevatedButton(
                onPressed: _isLoading ? null : _handleLogin,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child:
                    _isLoading
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                        : Text(
                          _isRegisterMode ? '注册' : '登录',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
              ),

              const SizedBox(height: 40), // 替换 Spacer 为固定高度
            ],
          ),
        ),
      ),
    );
  }
}
