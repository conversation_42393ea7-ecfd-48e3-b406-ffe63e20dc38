import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'pages/home_page.dart';
import 'pages/search_page.dart';
import 'pages/chat_page.dart';
import 'pages/profile_page.dart';
import 'pages/splash_screen.dart';
import 'pages/login_page.dart';
import 'pages/dev/pocketbase_test_page.dart';
import 'services/enhanced_auth_service.dart';
import 'config/app_config.dart';
import 'config/pocketbase_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化 PocketBase 客户端
  await PocketBaseConfig.instance.initialize();

  // 打印配置信息（仅在开发模式下）
  AppConfig.instance.printConfigInfo();

  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '鱼窝子',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color.fromARGB(255, 0, 136, 255),
        ),
        useMaterial3: true,
      ),
      initialRoute: '/splash',
      routes: {
        '/splash': (context) => const SplashScreen(),
        '/login': (context) => const LoginPage(),
        '/main': (context) => const MainScreen(),
        // 开发页面路由（仅在开发模式下可用）
        if (AppConfig.instance.enablePocketBaseTestPage)
          '/dev/pocketbase-test': (context) => const PocketBaseTestPage(),
      },
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with WidgetsBindingObserver {
  int _currentIndex = 0;

  // 页面列表
  late final List<Widget> _pages;

  // 认证服务
  final EnhancedAuthService _authService = EnhancedAuthService();

  @override
  void initState() {
    super.initState();

    // 添加应用生命周期监听
    WidgetsBinding.instance.addObserver(this);

    // 直接创建页面列表
    _pages = [
      const HomePage(),
      const SearchPage(),
      const ChatPage(),
      const ProfilePage(),
    ];
  }

  @override
  void dispose() {
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        // 应用从后台恢复时，静默尝试刷新登录状态
        _refreshAuthStateOnResume();
        break;
      case AppLifecycleState.paused:
        // 应用切换到后台时的处理
        debugPrint('应用切换到后台');
        break;
      case AppLifecycleState.detached:
        // 应用即将终止时的处理
        debugPrint('应用即将终止');
        break;
      case AppLifecycleState.inactive:
        // 应用失去焦点时的处理
        debugPrint('应用失去焦点');
        break;
      case AppLifecycleState.hidden:
        // 应用被隐藏时的处理
        debugPrint('应用被隐藏');
        break;
    }
  }

  /// 应用恢复时刷新认证状态
  Future<void> _refreshAuthStateOnResume() async {
    try {
      // 静默尝试刷新登录状态，不显示任何错误提示
      if (!_authService.isLoggedIn) {
        await _authService.initialize();
      }
    } catch (e) {
      // 静默处理错误，不影响用户体验
      debugPrint('应用恢复时刷新认证状态失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // 阻止默认的返回行为
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // 按返回键时将应用切换到后台，而不是退出
          _moveAppToBackground();
        }
      },
      child: Scaffold(
        body: IndexedStack(index: _currentIndex, children: _pages),
        bottomNavigationBar: BottomNavigationBar(
          backgroundColor: Theme.of(context).primaryColor,
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.shifting,
          selectedItemColor: Theme.of(context).primaryColor,
          unselectedItemColor: Theme.of(context).primaryColor,
          items: const [
            BottomNavigationBarItem(
              icon: FaIcon(FontAwesomeIcons.house),
              label: '主页',
            ),
            BottomNavigationBarItem(
              icon: FaIcon(FontAwesomeIcons.magnifyingGlass),
              label: '搜索',
            ),
            BottomNavigationBarItem(
              icon: FaIcon(FontAwesomeIcons.message),
              label: '消息',
            ),
            BottomNavigationBarItem(
              icon: FaIcon(FontAwesomeIcons.user),
              label: '我的',
            ),
          ],
        ),
      ),
    );
  }

  /// 将应用切换到后台
  void _moveAppToBackground() {
    SystemNavigator.pop();
  }
}
