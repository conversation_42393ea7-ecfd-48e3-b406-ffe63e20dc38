import 'package:flutter/material.dart';
import 'package:pocketbase/pocketbase.dart';
import 'app_config.dart';

/// PocketBase 客户端配置和初始化
class PocketBaseConfig {
  // 私有构造函数，确保单例模式
  PocketBaseConfig._();

  static final PocketBaseConfig _instance = PocketBaseConfig._();
  static PocketBaseConfig get instance => _instance;

  late PocketBase _client;
  bool _initialized = false;

  /// 获取 PocketBase 客户端实例
  PocketBase get client {
    if (!_initialized) {
      throw Exception('PocketBase 客户端尚未初始化，请先调用 initialize()');
    }
    return _client;
  }

  /// 初始化 PocketBase 客户端
  Future<void> initialize() async {
    try {
      final config = AppConfig.instance;
      _client = PocketBase(config.pocketBaseUrl);

      // 设置语言
      _client.lang = 'zh-CN';

      // 测试连接
      await _testConnection();

      _initialized = true;

      debugPrint('客户端初始化成功');
      debugPrint('服务器地址: ${config.pocketBaseUrl}');
    } catch (e) {
      debugPrint('PocketBase 客户端初始化失败: $e');
      rethrow;
    }
  }

  /// 测试连接
  Future<void> _testConnection() async {
    try {
      // 尝试获取应用信息来测试连接
      await _client.health.check();
    } catch (e) {
      throw Exception('无法连接到 PocketBase 服务器: $e');
    }
  }

  /// 检查是否已初始化
  bool get isInitialized => _initialized;

  /// 获取服务器信息
  String get serverUrl => AppConfig.instance.pocketBaseUrl;

  /// 获取当前认证状态
  bool get isAuthenticated => _client.authStore.isValid;

  /// 获取当前用户信息
  RecordModel? get currentUser => _client.authStore.record;

  /// 获取当前用户 ID
  String? get currentUserId => _client.authStore.record?.id;

  /// 获取认证 token
  String get authToken => _client.authStore.token;

  /// 清除认证信息
  void clearAuth() {
    _client.authStore.clear();
  }

  /// 刷新认证 token
  Future<void> refreshAuth() async {
    if (!isAuthenticated) {
      throw Exception('用户未登录，无法刷新认证');
    }

    try {
      await _client.collection('users').authRefresh();
    } catch (e) {
      throw Exception('刷新认证失败: $e');
    }
  }

  /// 获取集合操作对象
  RecordService collection(String collectionName) {
    return _client.collection(collectionName);
  }

  /// 获取文件 URL
  String getFileUrl(RecordModel record, String filename, {String? thumb}) {
    return _client.files.getUrl(record, filename, thumb: thumb).toString();
  }

  /// 健康检查
  Future<Map<String, dynamic>> healthCheck() async {
    try {
      final health = await _client.health.check();
      return {'status': 'healthy', 'data': health.toJson()};
    } catch (e) {
      return {'status': 'unhealthy', 'error': e.toString()};
    }
  }
}

/// 全局 PocketBase 客户端实例
PocketBase get pb => PocketBaseConfig.instance.client;
